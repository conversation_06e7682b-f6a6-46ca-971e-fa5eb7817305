!pip install torch transformers sentence-transformers scikit-learn pandas opencv-python moviepy mediapipe

import os
import cv2
import mediapipe as mp
from moviepy.editor import VideoFileClip
from transformers import pipeline

# 1. Speech-to-Text Model (Whisper)
# Using a GPU (device=0) is highly recommended for Whisper
stt_pipeline = pipeline("automatic-speech-recognition", model="openai/whisper-base.en", device=0)
print("--> Whisper Speech-to-Text model loaded.")

# 2. Hand Gesture Model (MediaPipe)
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False, max_num_hands=1, min_detection_confidence=0.7)
mp_drawing = mp.solutions.drawing_utils
print("--> MediaPipe Hand Gesture model loaded.")

# 3. ZERO-SHOT TEXT-TO-INTENT NLP Model
# We replace our custom classifier with a powerful pre-trained model.
# facebook/bart-large-mnli is a popular choice for this task.
zero_shot_classifier = pipeline("zero-shot-classification", model="facebook/bart-large-mnli", device=0)
# Define our possible intents which will be the candidate labels
CANDIDATE_INTENTS = ["forward", "left", "right", "stop"]
print("--> Zero-Shot Intent NLP model loaded.")
print("\n" + "="*50 + "\nAll models are ready.\n" + "="*50)

def get_intent_from_text_zero_shot(transcript, confidence_threshold=0.60):
    """
    Classifies a text command into an intent using a zero-shot model.
    """
    if not transcript:
        return None

    print(f"[NLP] Classifying text: '{transcript}'")

    # The model returns scores for all candidate labels, sorted from highest to lowest.
    results = zero_shot_classifier(transcript, CANDIDATE_INTENTS)

    best_intent = results['labels'][0]
    best_score = results['scores'][0]

    print(f"[NLP] Top classification: '{best_intent}' with confidence: {best_score:.2f}")

    # Only return the intent if the model is confident enough
    if best_score > confidence_threshold:
        print(f"[NLP] Confidence is above threshold. Intent is '{best_intent}'.")
        return best_intent
    else:
        print(f"[NLP] Confidence is below threshold. Intent is uncertain.")
        return None

def get_intent_from_audio(audio_path):
    """
    Takes an audio file path, transcribes it, and classifies the intent using the zero-shot model.
    """
    try:
        print("\n[Audio] Transcribing speech to text...")
        transcription_result = stt_pipeline(audio_path)
        transcript = transcription_result['text'].strip().lower()

        # We now call our new zero-shot function
        return get_intent_from_text_zero_shot(transcript)

    except Exception as e:
        print(f"[Audio] Error processing audio: {e}")
        return None


def get_intent_from_video(video_path):
    """
    Analyzes a video for hand gestures using a prioritized check:
    1. Fist w/ Thumb (Left/Right)
    2. Open Palm (Stop)
    3. Thumbs Up (Forward)
    """
    print("\n[Video] Analyzing video for hand gestures...")
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened(): return None

    gesture_counts = {"left": 0, "right": 0, "forward": 0, "stop": 0, "unknown": 0}
    frame_count = 0

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret: break

        if frame_count % 5 == 0:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = hands.process(frame_rgb)

            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Collect key landmarks
                    thumb_tip = hand_landmarks.landmark[mp_hands.HandLandmark.THUMB_TIP]
                    index_tip = hand_landmarks.landmark[mp_hands.HandLandmark.INDEX_FINGER_TIP]
                    middle_tip = hand_landmarks.landmark[mp_hands.HandLandmark.MIDDLE_FINGER_TIP]
                    ring_tip = hand_landmarks.landmark[mp_hands.HandLandmark.RING_FINGER_TIP]
                    pinky_tip = hand_landmarks.landmark[mp_hands.HandLandmark.PINKY_TIP]
                    wrist = hand_landmarks.landmark[mp_hands.HandLandmark.WRIST]
                    index_pip = hand_landmarks.landmark[mp_hands.HandLandmark.INDEX_FINGER_PIP]
                    middle_pip = hand_landmarks.landmark[mp_hands.HandLandmark.MIDDLE_FINGER_PIP]
                    ring_pip = hand_landmarks.landmark[mp_hands.HandLandmark.RING_FINGER_PIP]
                    pinky_pip = hand_landmarks.landmark[mp_hands.HandLandmark.PINKY_PIP]
                    thumb_pip = hand_landmarks.landmark[mp_hands.HandLandmark.THUMB_IP]
                    fingers_curled = (index_tip.y > index_pip.y and
                                      middle_tip.y > middle_pip.y and
                                      ring_tip.y > ring_pip.y and
                                      pinky_tip.y > pinky_pip.y)
                    thumb_extended = thumb_tip.y < thumb_pip.y - 0.03
                    is_fist_with_thumb = fingers_curled and thumb_extended

                    fingers_open = (index_tip.y < index_pip.y and
                                    middle_tip.y < middle_pip.y and
                                    ring_tip.y < ring_pip.y and
                                    pinky_tip.y < pinky_pip.y)

                    is_thumbs_up = (thumb_extended and fingers_curled)


                    # PRIORITY 1: Check for Left/Right Fist
                    if is_fist_with_thumb:

                        if thumb_tip.x < wrist.x - 0.04:
                            gesture_counts["left"] += 1
                        elif thumb_tip.x > wrist.x + 0.04:
                            gesture_counts["right"] += 1
                        else: # Could be a thumbs up, check in the next step
                            if is_thumbs_up:
                                gesture_counts["forward"] += 1
                            else:
                                gesture_counts["unknown"] += 1

                    # PRIORITY 2: Check for Stop (Open Palm)
                    elif fingers_open:
                        gesture_counts["stop"] += 1

                    # PRIORITY 3: Check for Forward (Thumbs Up) if not caught by fist logic
                    elif is_thumbs_up:
                        gesture_counts["forward"] += 1

                    # FALLBACK
                    else:
                        gesture_counts["unknown"] += 1

        frame_count += 1

    cap.release()

    if sum(gesture_counts.values()) > 0:
        dominant_gesture = max(gesture_counts, key=gesture_counts.get)
        if dominant_gesture != "unknown" and gesture_counts[dominant_gesture] > 2:
             print(f"[Video] Detected Gesture Counts: {gesture_counts}")
             print(f"[Video] Detected Intent: '{dominant_gesture}'")
             return dominant_gesture

    print("[Video] No definitive gesture detected.")
    return None

def process_multimodal_command(video_path):
    """
    The main pipeline function with updated, more flexible decision logic.
    """
    print(f"\n{'='*20} PROCESSING NEW COMMAND: {video_path} {'='*20}")
    if not os.path.exists(video_path):
        print(f"Error: Video file not found at {video_path}"); return

    # --- Step 1: Extract Audio & Get Intents ---
    temp_audio_path = "temp_audio.wav"
    try:
        with VideoFileClip(video_path) as video_clip:
            video_clip.audio.write_audiofile(temp_audio_path, logger=None)
        audio_intent = get_intent_from_audio(temp_audio_path)
    except Exception:
        audio_intent = None # Assume no audio if extraction fails
    finally:
        if os.path.exists(temp_audio_path): os.remove(temp_audio_path)

    video_intent = get_intent_from_video(video_path)

    # --- Step 2: NEW DECISION LOGIC ---
    print("\n[Fusion] Comparing intents...")
    print(f"[Fusion] Audio Intent: {audio_intent} | Video Intent: {video_intent}")

    # Case 1: High confidence match
    if audio_intent and video_intent and audio_intent == video_intent:
        print(f"\nHIGH CONFIDENCE: Intents match! Executing command: {audio_intent.upper()}")
        # Your robot action call, e.g., move_robot(audio_intent)

    # Case 2: Conflict
    elif audio_intent and video_intent and audio_intent != video_intent:
        print(f"\n CONFLICT: Audio detected '{audio_intent}' but Video detected '{video_intent}'. No action taken.")

    # Case 3: Audio only
    elif audio_intent and not video_intent:
        print(f"\n AUDIO ONLY: Proceeding with audio command: {audio_intent.upper()}")
        # Your robot action call, e.g., move_robot(audio_intent)

    # Case 4: Video only
    elif video_intent and not audio_intent:
        print(f"\n VIDEO ONLY: Proceeding with video command: {video_intent.upper()}")
        # Your robot action call, e.g., move_robot(video_intent)

    # Case 5: No intent detected
    else: # This covers the case where both are None
        print("\nFAILED: No clear audio or video intent was detected. Please try again.")


if __name__ == "__main__":

    test_videos = [
        "/content/Screencast from 2025-09-11 17-35-22.webm",
    ]

    for video_file in test_videos:
        process_multimodal_command(video_file)

if __name__ == "__main__":

    test_videos = [
        "/content/Screencast from 2025-09-11 17-38-20.webm",
    ]

    for video_file in test_videos:
        process_multimodal_command(video_file)

if __name__ == "__main__":

    test_videos = [
        "/content/stop_merged.mp4",
    ]

    for video_file in test_videos:
        process_multimodal_command(video_file)

if __name__ == "__main__":

    test_videos = [
        "/content/left me.mp4",
    ]

    for video_file in test_videos:
        process_multimodal_command(video_file)

import cv2
import mediapipe as mp
import time
import numpy as np

def process_webcam_realtime():
    """
    Captures video from the webcam, processes frames for hand gestures in real-time,
    and displays the results.
    """
    print("\n[Webcam] Starting real-time webcam processing...")

    cap = cv2.VideoCapture(0)  # 0 is typically the default webcam
    if not cap.isOpened():
        print("[Webcam] Error: Could not open webcam.")
        return

    # Initialize MediaPipe Hands outside the loop for efficiency
    mp_hands = mp.solutions.hands
    hands = mp_hands.Hands(static_image_mode=False, max_num_hands=1, min_detection_confidence=0.7)
    mp_drawing = mp.solutions.drawing_utils

    last_intent_time = time.time()
    intent_cooldown = 1.0 # seconds to wait before detecting the same intent again

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # Flip the frame horizontally for a more intuitive mirror-like view
        frame = cv2.flip(frame, 1)

        # Convert the frame to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Process the frame with MediaPipe Hands
        results = hands.process(frame_rgb)

        current_intent = None

        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw hand landmarks on the frame
                mp_drawing.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

                # --- Gesture Detection Logic (similar to get_intent_from_video) ---
                thumb_tip = hand_landmarks.landmark[mp_hands.HandLandmark.THUMB_TIP]
                index_tip = hand_landmarks.landmark[mp_hands.HandLandmark.INDEX_FINGER_TIP]
                middle_tip = hand_landmarks.landmark[mp_hands.HandLandmark.MIDDLE_FINGER_TIP]
                ring_tip = hand_landmarks.landmark[mp_hands.HandLandmark.RING_FINGER_TIP]
                pinky_tip = hand_landmarks.landmark[mp_hands.HandLandmark.PINKY_TIP]
                wrist = hand_landmarks.landmark[mp_hands.HandLandmark.WRIST]
                index_pip = hand_landmarks.landmark[mp_hands.HandLandmark.INDEX_FINGER_PIP]
                middle_pip = hand_landmarks.landmark[mp_hands.HandLandmark.MIDDLE_FINGER_PIP]
                ring_pip = hand_landmarks.landmark[mp_hands.HandLandmark.RING_FINGER_PIP]
                pinky_pip = hand_landmarks.landmark[mp_hands.HandLandmark.PINKY_PIP]
                thumb_pip = hand_landmarks.landmark[mp_hands.HandLandmark.THUMB_IP]

                fingers_curled = (index_tip.y > index_pip.y and
                                  middle_tip.y > middle_pip.y and
                                  ring_tip.y > ring_pip.y and
                                  pinky_tip.y > pinky_pip.y)
                thumb_extended = thumb_tip.y < thumb_pip.y - 0.03

                fingers_open = (index_tip.y < index_pip.y and
                                middle_tip.y < middle_pip.y and
                                ring_tip.y < ring_pip.y and
                                pinky_tip.y < pinky_pip.y)

                is_thumbs_up = (thumb_extended and fingers_curled)


                # PRIORITY 1: Check for Left/Right Fist
                if fingers_curled and thumb_extended:
                    if thumb_tip.x < wrist.x - 0.04:
                        current_intent = "left"
                    elif thumb_tip.x > wrist.x + 0.04:
                        current_intent = "right"
                    elif is_thumbs_up:
                         current_intent = "forward"
                    else:
                        current_intent = "unknown"


                # PRIORITY 2: Check for Stop (Open Palm)
                elif fingers_open:
                    current_intent = "stop"

                # PRIORITY 3: Check for Forward (Thumbs Up) if not caught by fist logic
                elif is_thumbs_up:
                    current_intent = "forward"


                # Display the detected intent on the frame
                if current_intent and current_intent != "unknown":
                     cv2.putText(frame, f"Intent: {current_intent.upper()}", (10, 30),
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2, cv2.LINE_AA)

        # Display the frame
        # In Colab, cv2.imshow() is not directly supported. We'll use a different approach
        # to display images in the notebook output.
        # For simplicity here, we'll just print the detected intent.
        # A more advanced implementation would use IPython.display or similar.

        # Simple Cooldown mechanism to avoid rapid-fire intent detection
        if current_intent and current_intent != "unknown" and time.time() - last_intent_time > intent_cooldown:
            print(f"[Webcam] Detected Intent: {current_intent.upper()}")
            # Here you would add your action based on the detected intent
            # e.g., move_robot(current_intent)
            last_intent_time = time.time()


        # To display the frame in Colab, you would typically convert it to a format
        # that can be displayed in the notebook output, e.g., using IPython.display.Image
        # or by saving frames temporarily and displaying them. This requires
        # more complex handling of the display loop. For now, we focus on
        # the detection logic.

        # Example of how you might display in a local environment:
        # cv2.imshow('Webcam Feed', frame)
        # if cv2.waitKey(1) & 0xFF == ord('q'):
        #     break


    # Release the webcam and destroy windows (for local environment)
    cap.release()
    # cv2.destroyAllWindows()
    print("[Webcam] Real-time webcam processing stopped.")



if __name__ == "__main__":
    process_webcam_realtime()